/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: #333;
  overflow-x: hidden;
  background: #ffffff;
}

/* Ensure no white space above header on mobile */
html,
body {
  margin: 0;
  padding: 0;
  background: #ffffff;
}

/* Removed content-visibility for instant loading */

/* Performance: avoid FOIT when web fonts load */
:root {
  --font-inter: "Inter", system-ui, -apple-system, Segoe UI, Roboto, Helvetica,
    Arial, sans-serif;
  --font-serif: "Playfair Display", Georgia, "Times New Roman", Times, serif;
}

body {
  font-family: var(--font-inter);
}

/* Remove bold from all headings and ensure Inter font */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-inter) !important;
}

/* Preloader Styles */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  transition: opacity 0.8s ease, visibility 0.8s ease;
}

.preloader.fade-out {
  opacity: 0;
  visibility: hidden;
}

.preloader-content {
  text-align: center;
  color: white;
}

.logo-animation h1 {
  font-family: var(--font-inter);
  font-size: 4rem;
  font-weight: normal;
  margin-bottom: 2rem;
  letter-spacing: 0.2em;
  animation: logoGlow 2s ease-in-out infinite alternate;
}

.loading-bar {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  margin: 0 auto;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
  border-radius: 2px;
  animation: loadingProgress 2s ease-in-out;
  background-size: 200% 100%;
  animation: loadingProgress 2s ease-in-out, shimmer 1.5s ease-in-out infinite;
}

@keyframes logoGlow {
  0% {
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
  }
  100% {
    text-shadow: 0 0 40px rgba(255, 215, 0, 0.8),
      0 0 60px rgba(255, 215, 0, 0.6);
  }
}

@keyframes loadingProgress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: background 0.2s ease, box-shadow 0.2s ease;
}
.header.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.15);
}

.navbar {
  padding: 1rem 0;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo h2 {
  color: #333 !important; /* Changed back to black */
  font-weight: 600 !important;
  font-size: 1.5rem;
  margin-bottom: 0.2rem;
  font-family: var(--font-inter);
  letter-spacing: 0.05em;
  transition: color 0.3s ease; /* Added smooth transition */
}

.nav-logo:hover h2 {
  color: #2c5aa0 !important; /* Blue on hover */
}

.logo-link {
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #333 !important;
  font-weight: 500 !important;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #2c5aa0;
}

.nav-link::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -5px;
  left: 0;
  background-color: #2c5aa0;
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.nav-contact .phone-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #2c5aa0;
  text-decoration: none;
  font-weight: normal;
  padding: 0.5rem 1rem;
  border: 2px solid #2c5aa0;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.nav-contact .phone-link:hover {
  background: #2c5aa0;
  color: white;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 10px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  min-height: 44px;
  min-width: 44px;
  justify-content: center;
  align-items: center;
  z-index: 1002; /* Ensure hamburger is above the menu */
  position: relative;
}

.hamburger:hover {
  background-color: rgba(44, 90, 160, 0.1);
}

.hamburger:active {
  background-color: rgba(44, 90, 160, 0.2);
}

.bar {
  width: 25px;
  height: 3px;
  background: #333;
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
  display: block;
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  background: #ffffff;
  padding-top: 80px; /* Account for fixed header */
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%),
    radial-gradient(
      circle at 25% 25%,
      rgba(44, 90, 160, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(255, 215, 0, 0.05) 0%,
      transparent 50%
    );
  background-size: 100% 100%, 800px 800px, 600px 600px;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(44,90,160,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

.hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-content {
  text-align: left;
}

/* Mobile override for hero centering */
@media (max-width: 768px) {
  .hero {
    text-align: center !important;
    padding-top: 90px !important; /* Increased to push content below header */
    height: 100vh !important; /* Use full viewport height instead of min-height */
    min-height: 100vh !important;
    padding-bottom: 0 !important; /* Remove bottom padding to eliminate gap */
    background: #ffffff !important;
    margin-bottom: 0 !important; /* Ensure no margin */
    display: flex !important;
    align-items: center !important; /* Center content vertically */
    justify-content: center !important;
  }

  /* Force white background on mobile and hide complex patterns */
  .hero-background,
  .hero-pattern,
  .hero-overlay {
    display: none !important;
  }

  /* Ensure hero has solid white background */
  .hero::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffffff !important;
    z-index: 1;
  }

  .hero-container {
    text-align: center !important;
    justify-items: center !important;
    padding-top: 1rem !important; /* Increased padding to push content down */
    position: relative;
    z-index: 2;
  }

  .hero-content {
    text-align: center !important;
    position: relative;
    z-index: 2;
  }

  .hero-content * {
    text-align: center !important;
  }

  .hero-title {
    margin-top: 0 !important;
    padding-top: 0 !important;
    line-height: 1.2 !important;
  }

  /* Shorter service card descriptions for mobile */
  .service-card {
    height: 360px !important;
  }

  .service-card p {
    display: none !important;
  }

  .service-card-content {
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    justify-content: space-between !important;
  }

  .service-card-content h3 {
    height: 4.5rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    margin-bottom: 1rem !important;
    line-height: 1.3 !important;
  }

  .service-card:nth-child(1) .service-card-content::after {
    content: "High-quality precast concrete products for civil industry.";
    display: block;
    color: #666;
    line-height: 1.6;
    text-align: center;
  }

  .service-card:nth-child(2) .service-card-content::after {
    content: "Custom solutions designed for your specific project needs.";
    display: block;
    color: #666;
    line-height: 1.6;
    text-align: center;
  }

  .service-card:nth-child(3) .service-card-content::after {
    content: "Reliable delivery ensuring products arrive on time.";
    display: block;
    color: #666;
    line-height: 1.6;
    text-align: center;
  }

  .service-card:nth-child(4) .service-card-content::after {
    content: "Professional consultation to help choose the right solutions.";
    display: block;
    color: #666;
    line-height: 1.6;
    text-align: center;
  }
}

.hero-badge {
  display: inline-block;
  background: rgba(44, 90, 160, 0.1);
  color: #2c5aa0;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(44, 90, 160, 0.2);
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: #1a202c;
  font-family: var(--font-inter);
}

.hero-title .highlight {
  color: #2c5aa0;
  position: relative;
}

.hero-title .highlight::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #ffd700, #ffed4e);
  border-radius: 2px;
}

.hero-subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #64748b;
  margin-bottom: 2rem;
  max-width: 500px;
}

.hero-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #2c5aa0;
  font-family: var(--font-inter);
}

.stat-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

.hero-features {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1.5rem;
  margin-bottom: 2.5rem;
}

.feature-pill {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  padding: 0.75rem 1.25rem;
  border-radius: 25px;
  border: 1px solid #e2e8f0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #475569;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.feature-pill:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(44, 90, 160, 0.15);
  border-color: #2c5aa0;
}

.feature-pill i {
  color: #2c5aa0;
  font-size: 1rem;
}

/* Hero Visual Card */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.visual-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  max-width: 500px;
  width: 100%;
}

.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.card-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  font-family: var(--font-inter);
}

.commitment-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.commitment-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.commitment-item:hover {
  background: #f8fafc;
  transform: translateX(5px);
}

.commitment-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.commitment-icon.quality {
  background: linear-gradient(135deg, #10b981, #059669);
}

.commitment-icon.availability {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.commitment-icon.reliability {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.commitment-icon.affordability {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.commitment-text h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.25rem;
}

.commitment-text p {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.4;
}

.hero-cta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

.scroll-arrow {
  width: 30px;
  height: 30px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-top: none;
  border-right: none;
  transform: rotate(-45deg);
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1.2rem 2.5rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.4s ease;
  border: 2px solid transparent;
  cursor: pointer;
  font-size: 1rem;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #2c5aa0, #4a90e2);
  color: white;
  border-color: #2c5aa0;
  box-shadow: 0 4px 15px rgba(44, 90, 160, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #4a90e2, #6bb6ff);
  color: white;
  border-color: #4a90e2;
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(74, 144, 226, 0.4);
}

.btn-outline {
  background: #1e3a6f; /* Use the darker blue as default (same as hover) */
  color: white;
  border-color: #1e3a6f;
  box-shadow: 0 8px 25px rgba(30, 58, 111, 0.2);
}

.btn-outline:hover {
  background: #2c5aa0; /* Lighter blue on hover */
  color: white;
  border-color: #2c5aa0;
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(44, 90, 160, 0.4);
}

.btn i {
  transition: transform 0.3s ease;
}

.btn:hover i {
  transform: translateX(5px);
}

/* Section Styles */
section {
  padding: 5rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  font-family: var(--font-inter);
  position: relative;
}

.section-header h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
  border-radius: 2px;
}

.section-header p {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

/* About Section */
.about {
  background: #f8f9fa;
}

/* Mobile: Ensure consistent white background */
@media (max-width: 768px) {
  .about {
    background: #ffffff !important; /* Match hero background on mobile */
  }
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.about-text {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.about-item h3 {
  color: #2c5aa0;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.about-item p {
  color: #666;
  line-height: 1.8;
}

.about-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card i {
  font-size: 2.5rem;
  color: #2c5aa0;
  margin-bottom: 1rem;
}

.feature-card h4 {
  color: #333;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.feature-card p {
  color: #666;
  font-size: 0.9rem;
}

/* Services Section */
.services-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

.service-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  border: 1px solid #eee;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #2c5aa0, #667eea, #2c5aa0);
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 25px 50px rgba(44, 90, 160, 0.15);
}

.service-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.service-card i {
  font-size: 2.5rem;
  color: #2c5aa0;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(44, 90, 160, 0.1);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.service-card:hover i {
  background: #2c5aa0;
  color: white;
  transform: scale(1.1);
}

.service-card h3 {
  color: #333;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  font-weight: 600;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1.3;
}

.service-card p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* Gallery Section */
.gallery {
  background: #f8f9fa;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.gallery-item {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  aspect-ratio: 4/3;
  cursor: pointer;
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 90, 160, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Only apply hover effects on devices that support hover */
@media (hover: hover) and (pointer: fine) {
  .gallery-item:hover .gallery-overlay {
    opacity: 1;
  }

  .gallery-item:hover img {
    transform: scale(1.1);
  }
}

.gallery-overlay i {
  color: white;
  font-size: 2rem;
}

/* Contact Section */
.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.contact-item i {
  font-size: 1.5rem;
  color: #2c5aa0;
  margin-top: 0.2rem;
}

.contact-item h4 {
  color: #333;
  margin-bottom: 0.5rem;
}

.contact-item p {
  color: #666;
  line-height: 1.6;
}

.contact-item a {
  color: #2c5aa0;
  text-decoration: none;
}

.contact-item a:hover {
  text-decoration: underline;
}

.contact-form {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.contact-form::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #2c5aa0, #667eea, #2c5aa0);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1.2rem;
  border: 2px solid #eee;
  border-radius: 12px;
  font-family: inherit;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #fafafa;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2c5aa0;
  background: white;
  box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
  transform: translateY(-2px);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* Footer */
.footer {
  background: #2c5aa0;
  color: white;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
  justify-items: start;
}

@media (max-width: 768px) {
  .footer-content {
    justify-items: center;
  }
}

.footer-section {
  text-align: left;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 1rem;
  color: #ffd700;
}

.footer-section p {
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.footer-section i {
  margin-right: 0.5rem;
  color: #ffd700;
}

.footer-section a {
  color: #ffffff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: #ffd700;
  text-decoration: underline;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  opacity: 0.8;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footer-bottom p {
  margin: 0;
  width: 100%;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Logo Link Styles */
.logo-link {
  text-decoration: none;
  color: inherit;
  display: block;
  transition: opacity 0.3s ease;
}

.logo-link:hover {
  opacity: 0.8;
}

.logo-link h2 {
  margin: 0;
}

/* Responsive Design */
/* Extra Small Devices (320px to 480px) */
@media (max-width: 480px) {
  .hamburger {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: white;
    width: 100%;
    text-align: center;
    transition: left 0.3s ease;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 1.5rem 0;
    z-index: 1001; /* Increased z-index to ensure it appears above other elements */
    max-height: calc(100vh - 70px);
    overflow-y: auto;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }

  .nav-menu.active {
    left: 0;
    display: flex; /* Ensure it's displayed when active */
  }

  .nav-menu li {
    margin: 0.2rem 0;
  }

  .nav-link {
    font-size: 1.1rem;
    padding: 0.5rem;
    display: block;
    font-weight: 500;
  }

  .nav-contact {
    display: none;
  }

  .navbar {
    padding: 0.8rem 0;
  }
  .container {
    padding: 0 12px; /* Reduced padding for very small screens */
  }

  .nav-container {
    padding: 0 12px; /* Reduced padding for very small screens */
  }

  .nav-logo h2 {
    font-size: 1.1rem; /* Slightly smaller logo on very small screens */
  }

  .hero {
    padding-top: 75px;
    min-height: 100vh;
    text-align: center;
    padding-bottom: 2rem;
  }

  .hero-container {
    text-align: center !important;
    padding: 0 20px;
    margin: 0 auto;
    width: 100%;
  }

  .hero-content {
    text-align: center !important;
    width: 100%;
    margin: 0 auto;
  }

  .hero-title {
    font-size: 1.8rem; /* Reduced for better mobile fit */
    line-height: 1.2;
    text-align: center !important;
    margin: 0 auto 1rem auto;
    width: 100%;
    padding: 0 10px; /* Add padding to prevent edge cutoff */
    word-wrap: break-word;
  }

  .hero-subtitle {
    font-size: 0.95rem; /* Slightly smaller for mobile */
    margin: 0 auto 1.2rem auto; /* Reduced margin */
    text-align: center !important;
    width: 100%;
    padding: 0 15px; /* Add padding to prevent edge cutoff */
    line-height: 1.4;
  }

  .hero-stats {
    justify-content: center !important;
    text-align: center !important;
    margin: 0 auto 1.2rem auto; /* Reduced margin */
    width: 100%;
    flex-wrap: wrap; /* Allow wrapping on very small screens */
    gap: 1rem; /* Reduced gap */
    padding: 0 10px; /* Add padding */
  }

  .stat-item {
    text-align: center !important;
    min-width: 80px; /* Ensure minimum width */
    flex: 1; /* Allow flexible sizing */
  }

  .hero-features {
    justify-content: center !important;
    text-align: center !important;
    margin: 1rem auto 2rem auto; /* Reduced margins */
    width: 100%;
    padding: 0 10px; /* Add padding to prevent edge cutoff */
  }

  .btn {
    padding: 0.9rem 1.3rem; /* Slightly reduced padding */
    font-size: 0.85rem; /* Smaller font for mobile */
    min-height: 44px;
    min-width: 44px;
  }

  .hero-cta {
    flex-direction: column;
    gap: 0.8rem; /* Reduced gap */
    text-align: center !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 0 auto;
    width: 100%;
    padding: 0 15px; /* Add padding to prevent edge cutoff */
  }

  .hero-cta .btn {
    width: 100%;
    max-width: 260px; /* Reduced max width */
    justify-content: center !important;
    text-align: center !important;
    margin: 0 auto;
  }

  .feature-pill {
    font-size: 0.75rem; /* Smaller text for mobile */
    padding: 0.5rem 0.8rem; /* Reduced padding */
    margin: 0.2rem; /* Add small margin for spacing */
    white-space: nowrap; /* Prevent text wrapping */
  }

  /* Hide Our Commitment section on mobile */
  .hero-visual {
    display: none !important;
  }

  .section-header {
    text-align: center;
  }

  .section-header h2 {
    font-size: 1.8rem;
    text-align: center;
  }

  .section-header p {
    font-size: 0.9rem;
    text-align: center;
  }

  section {
    padding: 2.5rem 0;
    text-align: center;
  }

  .about-content {
    display: flex !important;
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .about-text {
    text-align: center;
  }

  .about-features {
    grid-template-columns: 1fr !important;
    gap: 1rem;
    text-align: center;
  }

  .about-item {
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .about-item h3 {
    font-size: 1.1rem;
    text-align: center;
  }

  .about-item p {
    font-size: 0.9rem;
    text-align: center;
  }

  /* Improve services section spacing on mobile */
  .services {
    padding-bottom: 4rem !important;
  }

  .services-grid {
    gap: 1rem;
    text-align: center;
    margin-bottom: 2rem;
  }

  .service-card {
    padding: 1.5rem;
    text-align: center;
  }

  .service-card h3 {
    text-align: center;
  }

  .service-card p {
    text-align: center;
  }

  /* Add extra bottom padding to last two service cards on mobile */
  .services-grid .service-card:nth-child(3),
  .services-grid .service-card:nth-child(4) {
    margin-bottom: 3rem !important;
    padding-bottom: 2rem !important;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    text-align: center;
    justify-items: center;
    overflow-x: hidden; /* Prevent horizontal scrolling */
    width: 100%;
  }

  .contact-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 2rem !important;
    text-align: center;
  }

  .contact-info {
    order: 1;
    width: 100%;
    text-align: center;
  }

  .contact-item {
    text-align: center;
    justify-content: center;
    flex-direction: column !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  .contact-item i {
    margin-top: 0 !important;
    margin-bottom: 0.5rem;
    font-size: 2rem !important;
  }

  .contact-item h4 {
    text-align: center;
    margin-bottom: 0.3rem !important;
  }

  .contact-item p {
    text-align: center;
  }

  .contact-form {
    order: 2;
    width: 100%;
    padding: 1.5rem;
    text-align: center;
  }

  .form-group input,
  .form-group textarea {
    padding: 1rem;
    font-size: 0.9rem;
    min-height: 44px;
  }

  .footer-content {
    gap: 1.5rem;
    grid-template-columns: 1fr;
    text-align: center;
    justify-items: center;
  }

  .footer-section {
    text-align: center;
    max-width: 300px;
  }
}

/* Small to Medium Mobile Devices (481px to 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 80px;
    flex-direction: column;
    background-color: white;
    width: 100%;
    text-align: center;
    transition: left 0.3s ease;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 2rem 0;
    z-index: 1001; /* Increased z-index to ensure it appears above other elements */
    max-height: calc(100vh - 80px);
    overflow-y: auto;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }

  .nav-menu.active {
    left: 0;
    display: flex; /* Ensure it's displayed when active */
  }

  .nav-menu li {
    margin: 0.2rem 0;
  }

  .nav-contact {
    display: none;
  }

  .hero {
    padding-top: 90px; /* Increased to match smaller screen breakpoint */
    padding-bottom: 2rem;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center !important;
    padding: 0 20px; /* Remove top padding to allow flexbox centering */
    margin: 0 auto;
    width: 100%;
    height: 100%; /* Take full height of hero */
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-evenly !important; /* Distribute space evenly */
    align-items: center !important;
  }

  .hero-content {
    text-align: center !important;
    width: 100%;
    margin: 0 auto;
  }

  .hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
    text-align: center;
  }

  .hero-subtitle {
    font-size: 1.1rem;
    text-align: center;
  }

  .hero-features {
    justify-content: center;
    gap: 0.8rem;
    margin-top: 1.5rem;
  }

  .hero-cta {
    justify-content: center;
    gap: 1rem;
  }

  .btn {
    min-height: 44px;
    min-width: 44px;
  }

  .visual-card {
    max-width: 400px;
    margin: 0 auto;
  }

  .about-content {
    grid-template-columns: 1fr !important;
    gap: 2rem;
    display: flex;
    flex-direction: column;
    text-align: center;
  }

  .about-text {
    text-align: center;
  }

  .about-item {
    text-align: center;
  }

  .about-features {
    grid-template-columns: 1fr !important;
    gap: 1rem;
    text-align: center;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    text-align: center;
  }

  .service-card {
    text-align: center;
  }

  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .contact-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 2rem !important;
  }

  .contact-info {
    order: 1;
    width: 100%;
  }

  .contact-item {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center;
    gap: 0.5rem !important;
  }

  .contact-item i {
    margin-top: 0 !important;
    margin-bottom: 0.5rem;
    font-size: 2rem !important;
  }

  .contact-item h4 {
    margin-bottom: 0.3rem !important;
  }

  .contact-form {
    order: 2;
    width: 100%;
  }

  .footer-content {
    grid-template-columns: repeat(2, 1fr);
    text-align: center;
    gap: 2rem;
    justify-items: center;
  }

  .footer-section {
    text-align: center;
  }

  section {
    padding: 3rem 0;
  }

  .section-header h2 {
    font-size: 2rem;
  }
}

/* Large Mobile/Small Tablet (769px to 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .hero-container {
    gap: 3rem;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .gallery-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .contact-content {
    gap: 3rem;
  }
}

/* Additional Mobile Improvements */
@media (max-width: 768px) {
  /* Hide desktop navigation and show hamburger */
  .nav-menu {
    display: none;
  }

  .nav-menu.active {
    display: flex !important;
    position: fixed;
    left: 0;
    top: 70px;
    flex-direction: column;
    background-color: white;
    width: 100%;
    text-align: center;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 1.5rem 0;
    z-index: 1001;
    max-height: calc(100vh - 70px);
    overflow-y: auto;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }

  .hamburger {
    display: flex !important;
  }

  /* Fix white space above header on mobile */
  html,
  body {
    background: #ffffff !important; /* Force white background */
    margin: 0;
    padding: 0;
    min-height: 100vh;
  }

  /* Ensure all sections have consistent background on mobile */
  section {
    background: #ffffff !important;
    margin: 0 !important;
  }

  /* Ensure header starts at very top */
  .header {
    top: 0;
    margin-top: 0;
    background: rgba(255, 255, 255, 0.95);
    left: 0;
    right: 0;
  }

  /* Ensure scrolled header consistency */
  .header.scrolled {
    background: rgba(255, 255, 255, 0.98);
  }

  /* Clean mobile navigation hover effects */
  .nav-link::after {
    display: none !important;
  }

  .nav-link {
    padding: 0.8rem 1.5rem !important;
    margin: 0.2rem 1rem !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
    display: block !important;
    color: #333 !important;
    text-decoration: none !important;
  }

  .nav-link:hover,
  .nav-link:focus {
    background-color: rgba(44, 90, 160, 0.1) !important;
    color: #2c5aa0 !important;
  }

  .nav-link:active {
    background-color: rgba(44, 90, 160, 0.2) !important;
  }

  .nav-link:hover {
    background-color: rgba(44, 90, 160, 0.1) !important;
    color: #2c5aa0 !important;
  }

  /* Shift hero content up and hide visual section */
  .hero {
    padding-top: 60px !important;
    margin-top: -10px !important;
    height: 100vh !important; /* Ensure full viewport height */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .hero-visual {
    display: none !important;
  }

  /* Ensure hero content is perfectly centered when visual is hidden */
  .hero-container {
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-evenly !important; /* Distribute space evenly */
    align-items: center !important;
    text-align: center !important;
    gap: 1.5rem !important; /* Slightly reduced gap */
    height: 100% !important; /* Take full height of hero */
    padding: 0 20px !important; /* Remove vertical padding */
  }

  .hero-content {
    width: 100% !important;
    max-width: 600px !important;
    margin: 0 auto !important;
    text-align: center !important;
  }

  /* Fix About section layout for mobile */
  .about-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 2rem !important;
    text-align: center !important;
  }

  .about-text {
    width: 100%;
    order: 1;
    text-align: center;
  }

  .about-item {
    text-align: center;
  }

  .about-item h3 {
    text-align: center;
  }

  .about-item p {
    text-align: center;
  }

  .about-features {
    width: 100%;
    order: 2;
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
    text-align: center;
  }

  /* Fix Contact section layout for mobile */
  .contact-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 2rem !important;
    text-align: center !important;
  }

  .contact-info {
    order: 1;
    width: 100%;
    text-align: center;
  }

  .contact-item {
    text-align: center;
    justify-content: center;
    flex-direction: column !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  .contact-item i {
    margin-top: 0 !important;
    margin-bottom: 0.5rem;
    font-size: 2rem !important;
  }

  .contact-item h4 {
    text-align: center;
    margin-bottom: 0.3rem !important;
  }

  .contact-item p {
    text-align: center;
  }

  .contact-form {
    order: 2;
    width: 100%;
    text-align: center;
  }

  /* Ensure all touch targets are at least 44px */
  .nav-link,
  .btn,
  .gallery-item,
  .service-card,
  .feature-card,
  .contact-item a,
  .phone-link {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve form accessibility */
  .form-group input,
  .form-group textarea,
  .form-group button {
    min-height: 48px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Better spacing for mobile */
  .hero-features {
    gap: 0.4rem; /* Reduced gap for tighter spacing */
    flex-wrap: wrap; /* Ensure wrapping on small screens */
    justify-content: center;
  }

  .feature-pill {
    padding: 0.5rem 0.8rem; /* Reduced padding */
    font-size: 0.8rem; /* Smaller font */
    margin: 0.1rem; /* Small margin for spacing */
    white-space: nowrap; /* Prevent text wrapping */
    min-width: auto; /* Allow flexible width */
  }

  /* Improve gallery on mobile */
  .gallery-item {
    min-height: 200px;
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
    /* Prevent scroll issues on mobile */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: auto;
  }

  /* Prevent touch callouts and selections that can interfere with scrolling */
  .gallery-item,
  .gallery-item img,
  .gallery-overlay {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* Ensure gallery section doesn't interfere with scrolling */
  .gallery {
    -webkit-overflow-scrolling: touch;
    overflow: visible;
  }

  .gallery-grid {
    overflow: visible;
  }

  /* Lightbox mobile optimizations */
  .lightbox {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Prevent body scroll when lightbox is open */
  body.lightbox-open {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
    height: 100% !important;
  }

  /* Better contact section */
  .contact-item {
    padding: 1.5rem 0;
    border-bottom: 1px solid #eee;
    flex-direction: column !important;
    align-items: center !important;
    text-align: center;
    gap: 0.5rem !important;
  }

  .contact-item:last-child {
    border-bottom: none;
  }

  .contact-item i {
    margin-top: 0 !important;
    margin-bottom: 0.5rem;
    font-size: 2rem !important;
    color: #2c5aa0;
  }

  .contact-item h4 {
    margin-bottom: 0.3rem !important;
    font-size: 1.1rem;
  }

  .contact-item p {
    margin: 0;
    font-size: 0.95rem;
  }

  /* Improve footer spacing */
  .footer-section {
    margin-bottom: 2rem;
  }

  .footer-section:last-child {
    margin-bottom: 0;
  }

  /* Fix text overflow */
  .hero-title,
  .section-header h2 {
    word-wrap: break-word;
    hyphens: auto;
  }

  /* Improve button spacing */
  .hero-cta {
    margin-top: 2rem;
  }

  /* Better visual card on mobile */
  .visual-card {
    padding: 1.5rem;
    margin: 1rem 0;
    text-align: center;
  }

  .visual-card h3 {
    text-align: center;
  }

  .visual-card p {
    text-align: center;
  }

  /* Ensure about section is properly stacked */
  .about-content {
    display: flex !important;
    flex-direction: column !important;
  }

  .about-text {
    order: 1;
  }

  .about-features {
    order: 2;
    display: grid !important;
    grid-template-columns: 1fr !important;
  }

  .feature-card {
    margin-bottom: 1rem;
  }

  /* Improve service cards */
  .service-card {
    margin-bottom: 1rem;
  }

  /* Fix horizontal scrolling */
  body {
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw; /* Prevent viewport overflow */
  }

  .hero-container,
  .container {
    max-width: 100%;
    overflow-x: hidden;
    box-sizing: border-box; /* Ensure padding is included in width */
  }

  /* Ensure all elements respect viewport width */
  * {
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Prevent text from causing horizontal overflow */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  /* Ensure stats don't overflow */
  .stat-number {
    font-size: 1.8rem; /* Reduced from default */
  }

  .stat-label {
    font-size: 0.8rem; /* Smaller label text */
  }
}

/* Portrait mobile - ensure content fits */
@media (max-width: 768px) and (orientation: portrait) {
  .hero {
    padding-top: 80px !important; /* Reduced padding to allow more content space */
    height: 100vh !important; /* Use full viewport height */
    min-height: 100vh !important;
    display: flex !important;
    align-items: center !important; /* Center content vertically */
    justify-content: center !important;
    padding-bottom: 0 !important; /* Remove bottom padding to eliminate gap */
    background: #ffffff !important; /* Ensure white background */
    margin-bottom: 0 !important; /* Remove any margin */
  }

  .hero-container {
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-evenly !important; /* Distribute space evenly */
    align-items: center !important;
    padding: 0 12px !important; /* Only horizontal padding */
  }

  .about {
    background: #ffffff !important; /* Match hero background */
    padding-top: 0 !important; /* Remove top padding to eliminate gap */
    margin-top: 0 !important; /* Remove any margin */
  }

  .hero-container {
    margin-top: 0.5rem !important; /* Slightly increased margin */
    width: 100% !important;
    padding: 0 12px !important; /* Reduced padding */
  }

  .hero-title {
    font-size: 1.7rem !important; /* Slightly smaller */
    line-height: 1.2 !important;
    margin-bottom: 0.8rem !important; /* Reduced margin */
    padding: 0 8px !important; /* Add padding */
    font-family: var(--font-inter) !important; /* Ensure consistent font */
    font-weight: 700 !important; /* Ensure bold weight */
    color: #1a202c !important; /* Ensure consistent color */
    text-align: center !important; /* Ensure centered */
  }

  .hero-subtitle {
    font-size: 0.9rem !important; /* Smaller subtitle */
    margin-bottom: 1rem !important; /* Reduced margin */
    padding: 0 12px !important; /* Add padding */
    font-family: var(--font-inter) !important; /* Ensure consistent font */
    color: #666 !important; /* Ensure consistent color */
    text-align: center !important; /* Ensure centered */
    line-height: 1.4 !important; /* Ensure good readability */
  }

  .hero-stats {
    margin-bottom: 1rem !important; /* Reduced margin */
    gap: 0.8rem !important; /* Reduced gap */
  }

  .hero-features {
    margin: 0.8rem auto 1.5rem auto !important; /* Reduced margins */
    gap: 0.3rem !important; /* Tighter gap */
  }

  .hero-cta {
    margin-top: 1.5rem !important; /* Reduced margin */
  }
}

/* Very small mobile devices (iPhone SE, etc.) */
@media (max-width: 375px) {
  .hero {
    padding-top: 100px !important; /* Extra padding for very small screens */
  }

  .hero-container {
    padding-top: 0.8rem !important; /* Additional container padding */
  }

  .hero-title {
    font-size: 1.6rem !important;
    padding: 0 8px !important;
    margin-bottom: 0.8rem !important;
  }

  .hero-subtitle {
    font-size: 0.85rem !important;
    padding: 0 12px !important;
    margin-bottom: 1rem !important;
  }

  .hero-stats {
    gap: 0.6rem !important;
    flex-wrap: wrap !important;
  }

  .stat-item {
    min-width: 70px !important;
  }

  .stat-number {
    font-size: 1.6rem !important;
  }

  .stat-label {
    font-size: 0.75rem !important;
  }

  .feature-pill {
    font-size: 0.7rem !important;
    padding: 0.4rem 0.6rem !important;
  }

  .btn {
    font-size: 0.8rem !important;
    padding: 0.8rem 1.1rem !important;
  }

  .hero-cta .btn {
    max-width: 240px !important;
  }

  .container {
    padding: 0 10px !important;
  }

  .nav-container {
    padding: 0 10px !important;
  }
}

/* Landscape orientation fixes */
@media (max-width: 768px) and (orientation: landscape) {
  .hero {
    min-height: 100vh;
    padding-top: 60px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  section {
    padding: 2rem 0;
  }
}

/* High DPI/Retina Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .btn,
  .nav-link,
  .gallery-item {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Force white menu regardless of system dark mode preference */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
  .nav-menu {
    background-color: white !important;
    color: #333 !important;
  }

  .nav-link {
    color: #333 !important;
  }

  .nav-logo h2 {
    color: #333 !important; /* Changed back to black */
    font-weight: 600 !important;
  }
}

/* Force logo and navigation styling - highest specificity */
.header .nav-container .nav-logo h2,
.navbar .nav-container .nav-logo h2,
header .nav-container .nav-logo h2 {
  color: #333 !important; /* Changed back to black */
  font-weight: 600 !important;
  text-shadow: 1px 1px 0px rgba(0, 0, 0, 0.1) !important; /* Updated shadow to match black */
}

.header .nav-menu .nav-link,
.navbar .nav-menu .nav-link,
header .nav-menu .nav-link {
  color: #333 !important;
  font-weight: 500 !important;
}

/* Print styles */
@media print {
  .header,
  .nav-menu,
  .hamburger,
  .hero-cta,
  .gallery,
  .contact-form {
    display: none !important;
  }

  .hero {
    padding-top: 0;
    min-height: auto;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }
}

/* Tablet responsive */
@media (max-width: 1024px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);

    gap: 1.5rem;
  }
}

/* Back to Top Button - Mobile Only */
.back-to-top {
  display: none; /* Hidden by default */
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 50%;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
  font-size: 16px;
  opacity: 0;
  transform: translateY(10px);
}

.back-to-top.show {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  transform: translateY(0);
}

.back-to-top:hover {
  background: rgba(255, 255, 255, 1);
  color: #000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.back-to-top:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

/* Show back-to-top button only on mobile devices */
@media (max-width: 768px) {
  .back-to-top {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Hide on desktop */
@media (min-width: 769px) {
  .back-to-top {
    display: none !important;
  }
}

/* Force mobile hero styling to match simulator exactly - highest priority */
@media (max-width: 768px) {
  .hero-title,
  .hero .hero-title,
  .hero-container .hero-title {
    font-family: var(--font-inter) !important;
    font-weight: 700 !important;
    color: #1a202c !important;
    text-align: center !important;
    font-size: 1.7rem !important;
    line-height: 1.2 !important;
    margin-bottom: 0.8rem !important;
    padding: 0 8px !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }

  .hero-subtitle,
  .hero .hero-subtitle,
  .hero-container .hero-subtitle {
    font-family: var(--font-inter) !important;
    color: #666 !important;
    text-align: center !important;
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
    margin-bottom: 1rem !important;
    padding: 0 12px !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }

  /* Ensure hero container uses space-evenly distribution */
  .hero-container,
  .hero .hero-container {
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-evenly !important;
    align-items: center !important;
    height: 100% !important;
    padding: 0 12px !important;
    transform: translateY(
      -24px
    ) !important; /* Shift content up by 24px total */
  }

  /* Force white background and proper spacing */
  .hero,
  .hero-section {
    background: #ffffff !important;
    padding-top: 80px !important;
    height: 100vh !important;
    min-height: 100vh !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  /* Fix section header text scrolling issue on mobile */
  .section-header,
  .section-header h2,
  .section-header p {
    overflow: visible !important;
    -webkit-overflow-scrolling: auto !important;
    touch-action: manipulation !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    -webkit-tap-highlight-color: transparent !important;
    pointer-events: none !important; /* Prevent text interaction */
  }

  /* Allow normal page scrolling over gallery section */
  .gallery,
  .gallery .container,
  .gallery .section-header {
    overflow: visible !important;
    -webkit-overflow-scrolling: touch !important;
    touch-action: pan-y !important; /* Allow vertical scrolling only */
  }

  /* Allow normal page scrolling over about section */
  .about,
  .about .container,
  .about .section-header {
    overflow: visible !important;
    -webkit-overflow-scrolling: touch !important;
    touch-action: pan-y !important; /* Allow vertical scrolling only */
  }
}
